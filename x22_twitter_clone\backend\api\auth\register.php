<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(200);
    exit();
}

include_once '../../config/database.php';
include_once '../../models/User.php';

$database = new Database();
$db = $database->getConnection();

$user = new User($db);

$data = json_decode(file_get_contents("php://input"));

if(
    !empty($data->username) &&
    !empty($data->email) &&
    !empty($data->password) &&
    !empty($data->display_name)
) {
    $user->username = $data->username;
    $user->email = $data->email;
    $user->display_name = $data->display_name;
    $user->bio = isset($data->bio) ? $data->bio : "";

    // Check if email already exists
    if($user->emailExists()) {
        http_response_code(400);
        echo json_encode(array("message" => "Email already exists."));
        exit();
    }

    // Check if username already exists
    if($user->usernameExists()) {
        http_response_code(400);
        echo json_encode(array("message" => "Username already exists."));
        exit();
    }

    // Hash the password
    $user->password_hash = password_hash($data->password, PASSWORD_DEFAULT);

    if($user->create()) {
        http_response_code(201);
        echo json_encode(array("message" => "User was created successfully."));
    } else {
        http_response_code(503);
        echo json_encode(array("message" => "Unable to create user."));
    }
} else {
    http_response_code(400);
    echo json_encode(array("message" => "Unable to create user. Data is incomplete."));
}
?>
