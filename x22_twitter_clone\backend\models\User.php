<?php
class User {
    private $conn;
    private $table_name = "users";

    public $id;
    public $username;
    public $email;
    public $password_hash;
    public $display_name;
    public $bio;
    public $profile_image_url;
    public $cover_image_url;
    public $followers_count;
    public $following_count;
    public $tweets_count;
    public $verified;
    public $created_at;
    public $updated_at;

    public function __construct($db) {
        $this->conn = $db;
    }

    // Create user
    function create() {
        $query = "INSERT INTO " . $this->table_name . " 
                SET username=:username, email=:email, password_hash=:password_hash, display_name=:display_name, bio=:bio";

        $stmt = $this->conn->prepare($query);

        // Sanitize
        $this->username = htmlspecialchars(strip_tags($this->username));
        $this->email = htmlspecialchars(strip_tags($this->email));
        $this->password_hash = htmlspecialchars(strip_tags($this->password_hash));
        $this->display_name = htmlspecialchars(strip_tags($this->display_name));
        $this->bio = htmlspecialchars(strip_tags($this->bio));

        // Bind values
        $stmt->bindParam(":username", $this->username);
        $stmt->bindParam(":email", $this->email);
        $stmt->bindParam(":password_hash", $this->password_hash);
        $stmt->bindParam(":display_name", $this->display_name);
        $stmt->bindParam(":bio", $this->bio);

        if($stmt->execute()) {
            return true;
        }

        return false;
    }

    // Read user by email
    function readByEmail() {
        $query = "SELECT id, username, email, password_hash, display_name, bio, profile_image_url, 
                         cover_image_url, followers_count, following_count, tweets_count, verified, created_at
                  FROM " . $this->table_name . " 
                  WHERE email = ? LIMIT 0,1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $this->email);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        if($row) {
            $this->id = $row['id'];
            $this->username = $row['username'];
            $this->password_hash = $row['password_hash'];
            $this->display_name = $row['display_name'];
            $this->bio = $row['bio'];
            $this->profile_image_url = $row['profile_image_url'];
            $this->cover_image_url = $row['cover_image_url'];
            $this->followers_count = $row['followers_count'];
            $this->following_count = $row['following_count'];
            $this->tweets_count = $row['tweets_count'];
            $this->verified = $row['verified'];
            $this->created_at = $row['created_at'];
            return true;
        }

        return false;
    }

    // Read user by ID
    function readById() {
        $query = "SELECT id, username, email, display_name, bio, profile_image_url, 
                         cover_image_url, followers_count, following_count, tweets_count, verified, created_at
                  FROM " . $this->table_name . " 
                  WHERE id = ? LIMIT 0,1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $this->id);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        if($row) {
            $this->username = $row['username'];
            $this->email = $row['email'];
            $this->display_name = $row['display_name'];
            $this->bio = $row['bio'];
            $this->profile_image_url = $row['profile_image_url'];
            $this->cover_image_url = $row['cover_image_url'];
            $this->followers_count = $row['followers_count'];
            $this->following_count = $row['following_count'];
            $this->tweets_count = $row['tweets_count'];
            $this->verified = $row['verified'];
            $this->created_at = $row['created_at'];
            return true;
        }

        return false;
    }

    // Check if email exists
    function emailExists() {
        $query = "SELECT id, username, email FROM " . $this->table_name . " WHERE email = ? LIMIT 0,1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $this->email);
        $stmt->execute();

        if($stmt->rowCount() > 0) {
            return true;
        }

        return false;
    }

    // Check if username exists
    function usernameExists() {
        $query = "SELECT id, username, email FROM " . $this->table_name . " WHERE username = ? LIMIT 0,1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $this->username);
        $stmt->execute();

        if($stmt->rowCount() > 0) {
            return true;
        }

        return false;
    }

    // Update user profile
    function update() {
        $query = "UPDATE " . $this->table_name . " 
                SET display_name = :display_name, bio = :bio, profile_image_url = :profile_image_url
                WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        $this->display_name = htmlspecialchars(strip_tags($this->display_name));
        $this->bio = htmlspecialchars(strip_tags($this->bio));
        $this->profile_image_url = htmlspecialchars(strip_tags($this->profile_image_url));

        $stmt->bindParam(':display_name', $this->display_name);
        $stmt->bindParam(':bio', $this->bio);
        $stmt->bindParam(':profile_image_url', $this->profile_image_url);
        $stmt->bindParam(':id', $this->id);

        if($stmt->execute()) {
            return true;
        }

        return false;
    }
}
?>
