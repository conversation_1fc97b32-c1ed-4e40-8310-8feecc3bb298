// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appTitle => 'إكس22';

  @override
  String get home => 'الرئيسية';

  @override
  String get profile => 'الملف الشخصي';

  @override
  String get trends => 'الاتجاهات';

  @override
  String get settings => 'الإعدادات';

  @override
  String get notifications => 'الإشعارات';

  @override
  String get bookmarks => 'المفضلة';

  @override
  String get help => 'المساعدة';

  @override
  String get logout => 'تسجيل الخروج';

  @override
  String get login => 'تسجيل الدخول';

  @override
  String get register => 'إنشاء حساب';

  @override
  String get email => 'البريد الإلكتروني';

  @override
  String get password => 'كلمة المرور';

  @override
  String get username => 'اسم المستخدم';

  @override
  String get displayName => 'الاسم المعروض';

  @override
  String get bio => 'النبذة الشخصية';

  @override
  String get confirmPassword => 'تأكيد كلمة المرور';

  @override
  String get createAccount => 'إنشاء حساب';

  @override
  String get signIn => 'تسجيل الدخول';

  @override
  String get welcomeBack => 'مرحباً بعودتك';

  @override
  String get signInToAccount => 'سجل دخولك إلى حسابك';

  @override
  String get joinToday => 'انضم إلى إكس22 اليوم';

  @override
  String get alreadyHaveAccount => 'لديك حساب بالفعل؟ سجل دخولك';

  @override
  String get dontHaveAccount => 'ليس لديك حساب؟ أنشئ حساباً';

  @override
  String get whatsHappening => 'ماذا يحدث؟';

  @override
  String get postYourReply => 'انشر ردك';

  @override
  String get post => 'نشر';

  @override
  String get reply => 'رد';

  @override
  String get retweet => 'إعادة تغريد';

  @override
  String get like => 'إعجاب';

  @override
  String get share => 'مشاركة';

  @override
  String get following => 'متابَع';

  @override
  String get followers => 'المتابعون';

  @override
  String get tweets => 'التغريدات';

  @override
  String get replies => 'الردود';

  @override
  String get media => 'الوسائط';

  @override
  String get noTweetsYet => 'لا توجد تغريدات بعد';

  @override
  String get beFirstToShare => 'كن أول من يشارك شيئاً!';

  @override
  String get failedToLoadTweets => 'فشل في تحميل التغريدات';

  @override
  String get retry => 'إعادة المحاولة';

  @override
  String get trendingIn => 'رائج في';

  @override
  String get searchFor => 'البحث عن';

  @override
  String get notInterestedInTrend => 'غير مهتم بهذا الاتجاه';

  @override
  String get report => 'إبلاغ';

  @override
  String get account => 'الحساب';

  @override
  String get privacyAndSecurity => 'الخصوصية والأمان';

  @override
  String get blockedAccounts => 'الحسابات المحظورة';

  @override
  String get preferences => 'التفضيلات';

  @override
  String get darkMode => 'الوضع المظلم';

  @override
  String get useDarkTheme => 'استخدام المظهر المظلم';

  @override
  String get autoPlayVideos => 'تشغيل الفيديوهات تلقائياً';

  @override
  String get videosPlayAutomatically => 'تشغيل الفيديوهات تلقائياً';

  @override
  String get language => 'اللغة';

  @override
  String get content => 'المحتوى';

  @override
  String get dataUsage => 'استخدام البيانات';

  @override
  String get manageDownloadPreferences => 'إدارة تفضيلات التحميل';

  @override
  String get contentFilters => 'مرشحات المحتوى';

  @override
  String get manageWhatContentYouSee => 'إدارة المحتوى الذي تراه';

  @override
  String get support => 'الدعم';

  @override
  String get helpCenter => 'مركز المساعدة';

  @override
  String get sendFeedback => 'إرسال ملاحظات';

  @override
  String get aboutX22 => 'حول إكس22';

  @override
  String get receiveNotifications => 'تلقي الإشعارات';

  @override
  String get changePassword => 'تغيير كلمة المرور';

  @override
  String get whoCanSeeYourTweets => 'من يمكنه رؤية تغريداتك';

  @override
  String get selectLanguage => 'اختر اللغة';

  @override
  String get tellUsWhatYouThink => 'أخبرنا برأيك...';

  @override
  String get cancel => 'إلغاء';

  @override
  String get send => 'إرسال';

  @override
  String get thankYouForFeedback => 'شكراً لك على ملاحظاتك!';

  @override
  String get areYouSureLogout => 'هل أنت متأكد من تسجيل الخروج؟';

  @override
  String get connectWithWorld => 'تواصل مع العالم';

  @override
  String get pleaseEnterEmail => 'يرجى إدخال بريدك الإلكتروني';

  @override
  String get pleaseEnterValidEmail => 'يرجى إدخال بريد إلكتروني صحيح';

  @override
  String get pleaseEnterPassword => 'يرجى إدخال كلمة المرور';

  @override
  String get passwordMustBe6Chars => 'يجب أن تكون كلمة المرور 6 أحرف على الأقل';

  @override
  String get pleaseEnterUsername => 'يرجى إدخال اسم المستخدم';

  @override
  String get usernameMustBe3Chars => 'يجب أن يكون اسم المستخدم 3 أحرف على الأقل';

  @override
  String get usernameOnlyLettersNumbers => 'يمكن أن يحتوي اسم المستخدم على أحرف وأرقام وشرطات سفلية فقط';

  @override
  String get pleaseEnterDisplayName => 'يرجى إدخال الاسم المعروض';

  @override
  String get displayNameMustBe2Chars => 'يجب أن يكون الاسم المعروض حرفين على الأقل';

  @override
  String get pleaseConfirmPassword => 'يرجى تأكيد كلمة المرور';

  @override
  String get passwordsDoNotMatch => 'كلمات المرور غير متطابقة';

  @override
  String get bioOptional => 'النبذة الشخصية (اختياري)';

  @override
  String get aboutYourself => 'أخبرنا عن نفسك...';

  @override
  String get accountCreatedSuccessfully => 'تم إنشاء الحساب بنجاح! يرجى تسجيل الدخول.';

  @override
  String get invalidPassword => 'كلمة مرور غير صحيحة.';

  @override
  String get userNotFound => 'المستخدم غير موجود.';

  @override
  String get emailAndPasswordRequired => 'البريد الإلكتروني وكلمة المرور مطلوبان.';

  @override
  String get networkError => 'خطأ في الشبكة';

  @override
  String get loginFailed => 'فشل تسجيل الدخول';

  @override
  String get registrationFailed => 'فشل التسجيل';

  @override
  String get failedToCreateTweet => 'فشل في إنشاء التغريدة';

  @override
  String get tweetCreatedSuccessfully => 'تم إنشاء التغريدة بنجاح.';

  @override
  String get unableToCreateTweet => 'غير قادر على إنشاء التغريدة. المحتوى مطلوب.';

  @override
  String get composeTweet => 'كتابة تغريدة';

  @override
  String get imageUploadComingSoon => 'رفع الصور قريباً!';

  @override
  String get gifSupportComingSoon => 'دعم الصور المتحركة قريباً!';

  @override
  String get shareComingSoon => 'وظيفة المشاركة قريباً!';

  @override
  String get notificationsComingSoon => 'الإشعارات قريباً!';

  @override
  String get bookmarksComingSoon => 'المفضلة قريباً!';

  @override
  String get helpComingSoon => 'المساعدة قريباً!';

  @override
  String get editProfileComingSoon => 'تعديل الملف الشخصي قريباً!';

  @override
  String get blockedAccountsComingSoon => 'الحسابات المحظورة قريباً!';

  @override
  String get darkModeComingSoon => 'الوضع المظلم قريباً!';

  @override
  String get dataUsageComingSoon => 'إعدادات استخدام البيانات قريباً!';

  @override
  String get contentFiltersComingSoon => 'مرشحات المحتوى قريباً!';

  @override
  String get helpCenterComingSoon => 'مركز المساعدة قريباً!';

  @override
  String get changePasswordComingSoon => 'تغيير كلمة المرور قريباً!';

  @override
  String get privacySettingsComingSoon => 'إعدادات الخصوصية قريباً!';

  @override
  String get repliesComingSoon => 'الردود قريباً!';

  @override
  String get mediaComingSoon => 'الوسائط قريباً!';

  @override
  String get searchingFor => 'البحث عن';

  @override
  String get wontSeeThisTrend => 'لن ترى هذا الاتجاه بعد الآن';

  @override
  String get trendReported => 'تم الإبلاغ عن الاتجاه';

  @override
  String get failedToLoadUserTweets => 'فشل في تحميل التغريدات';

  @override
  String get version => 'الإصدار 1.0.0';
}
