# How to Add Cairo Font to X22 Twitter Clone

## Step 1: Download Cairo Font Files

1. Go to Google Fonts: https://fonts.google.com/specimen/Cairo
2. Click "Download family" to download the Cairo font family
3. Extract the downloaded ZIP file
4. Copy the following font files to `assets/fonts/` directory:
   - `Cairo-Regular.ttf`
   - `Cairo-Bold.ttf` 
   - `Cairo-Light.ttf`

## Step 2: Enable Cairo Font in pubspec.yaml

Uncomment the fonts section in `pubspec.yaml`:

```yaml
fonts:
  - family: Cairo
    fonts:
      - asset: assets/fonts/Cairo-Regular.ttf
      - asset: assets/fonts/Cairo-Bold.ttf
        weight: 700
      - asset: assets/fonts/Cairo-Light.ttf
        weight: 300
```

## Step 3: Update main.dart

Change the fontFamily line in `main.dart`:

```dart
fontFamily: localeProvider.isArabic ? 'Cairo' : null,
```

## Step 4: Run Flutter Commands

```bash
flutter pub get
flutter hot restart
```

The app will now use the beautiful Cairo font for Arabic text!
