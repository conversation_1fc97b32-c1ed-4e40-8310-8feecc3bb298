import 'package:flutter/material.dart';

class TrendsScreen extends StatefulWidget {
  const TrendsScreen({super.key});

  @override
  State<TrendsScreen> createState() => _TrendsScreenState();
}

class _TrendsScreenState extends State<TrendsScreen> {
  // Mock trending data - in a real app, this would come from an API
  final List<TrendingTopic> _trendingTopics = [
    TrendingTopic(
      hashtag: '#Flutter',
      tweetsCount: 15420,
      category: 'Technology',
    ),
    TrendingTopic(hashtag: '#X22', tweetsCount: 8930, category: 'Social Media'),
    TrendingTopic(
      hashtag: '#Programming',
      tweetsCount: 12340,
      category: 'Technology',
    ),
    TrendingTopic(
      hashtag: '#WebDevelopment',
      tweetsCount: 9876,
      category: 'Technology',
    ),
    TrendingTopic(hashtag: '#AI', tweetsCount: 23450, category: 'Technology'),
    TrendingTopic(
      hashtag: '#MachineLearning',
      tweetsCount: 18760,
      category: 'Technology',
    ),
    TrendingTopic(
      hashtag: '#OpenSource',
      tweetsCount: 7654,
      category: 'Technology',
    ),
    TrendingTopic(
      hashtag: '#Mobile',
      tweetsCount: 11230,
      category: 'Technology',
    ),
  ];

  final List<String> _categories = [
    'All',
    'Technology',
    'Social Media',
    'News',
    'Sports',
  ];
  String _selectedCategory = 'All';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Trends',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 1,
      ),
      body: Column(
        children: [
          // Category Filter
          Container(
            height: 50,
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _categories.length,
              itemBuilder: (context, index) {
                final category = _categories[index];
                final isSelected = category == _selectedCategory;

                return Padding(
                  padding: EdgeInsets.only(
                    left: index == 0 ? 16 : 8,
                    right: index == _categories.length - 1 ? 16 : 0,
                  ),
                  child: FilterChip(
                    label: Text(category),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() {
                        _selectedCategory = category;
                      });
                    },
                    backgroundColor: Colors.grey.shade100,
                    selectedColor: Colors.blue.shade100,
                    labelStyle: TextStyle(
                      color: isSelected ? Colors.blue : Colors.black,
                      fontWeight:
                          isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                );
              },
            ),
          ),

          // Trending Topics List
          Expanded(
            child: ListView.builder(
              itemCount: _getFilteredTopics().length,
              itemBuilder: (context, index) {
                final topic = _getFilteredTopics()[index];
                return _buildTrendingItem(topic, index + 1);
              },
            ),
          ),
        ],
      ),
    );
  }

  List<TrendingTopic> _getFilteredTopics() {
    if (_selectedCategory == 'All') {
      return _trendingTopics;
    }
    return _trendingTopics
        .where((topic) => topic.category == _selectedCategory)
        .toList();
  }

  Widget _buildTrendingItem(TrendingTopic topic, int rank) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: Colors.grey.shade200)),
      ),
      child: Row(
        children: [
          // Rank
          SizedBox(
            width: 30,
            child: Text(
              '$rank',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade600,
              ),
            ),
          ),

          const SizedBox(width: 16),

          // Trending Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Category
                Text(
                  'Trending in ${topic.category}',
                  style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                ),
                const SizedBox(height: 2),

                // Hashtag
                Text(
                  topic.hashtag,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 2),

                // Tweet count
                Text(
                  '${_formatNumber(topic.tweetsCount)} Tweets',
                  style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                ),
              ],
            ),
          ),

          // More options
          IconButton(
            icon: const Icon(Icons.more_vert),
            onPressed: () {
              _showTrendOptions(context, topic);
            },
          ),
        ],
      ),
    );
  }

  String _formatNumber(int number) {
    if (number < 1000) {
      return number.toString();
    } else if (number < 1000000) {
      return '${(number / 1000).toStringAsFixed(1)}K';
    } else {
      return '${(number / 1000000).toStringAsFixed(1)}M';
    }
  }

  void _showTrendOptions(BuildContext context, TrendingTopic topic) {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.search),
                title: Text('Search ${topic.hashtag}'),
                onTap: () {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Searching for ${topic.hashtag}...'),
                    ),
                  );
                },
              ),
              ListTile(
                leading: const Icon(Icons.notifications_off),
                title: const Text('Not interested in this trend'),
                onTap: () {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('You won\'t see this trend anymore'),
                    ),
                  );
                },
              ),
              ListTile(
                leading: const Icon(Icons.report),
                title: const Text('Report'),
                onTap: () {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Trend reported')),
                  );
                },
              ),
            ],
          ),
        );
      },
    );
  }
}

class TrendingTopic {
  final String hashtag;
  final int tweetsCount;
  final String category;

  TrendingTopic({
    required this.hashtag,
    required this.tweetsCount,
    required this.category,
  });
}
