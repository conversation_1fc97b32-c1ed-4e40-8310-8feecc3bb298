import 'package:flutter/material.dart';
import '../models/tweet.dart';

class TweetCard extends StatelessWidget {
  final Tweet tweet;
  final VoidCallback? onLike;
  final VoidCallback? onRetweet;
  final VoidCallback? onReply;
  final VoidCallback? onShare;

  const TweetCard({
    super.key,
    required this.tweet,
    this.onLike,
    this.onRetweet,
    this.onReply,
    this.onShare,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: Colors.grey.shade200)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // User info and tweet header
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // User avatar
                CircleAvatar(
                  radius: 24,
                  backgroundColor: Colors.blue,
                  backgroundImage:
                      tweet.user.profileImageUrl != null
                          ? NetworkImage(tweet.user.profileImageUrl!)
                          : null,
                  child:
                      tweet.user.profileImageUrl == null
                          ? Text(
                            tweet.user.displayName
                                .substring(0, 1)
                                .toUpperCase(),
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          )
                          : null,
                ),
                const SizedBox(width: 12),

                // User info and tweet content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // User name, username, and time
                      Row(
                        children: [
                          Text(
                            tweet.user.displayName,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          if (tweet.user.verified) ...[
                            const SizedBox(width: 4),
                            const Icon(
                              Icons.verified,
                              color: Colors.blue,
                              size: 16,
                            ),
                          ],
                          const SizedBox(width: 8),
                          Text(
                            '@${tweet.user.username}',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '·',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            tweet.timeAgo,
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),

                      // Tweet content
                      Text(
                        tweet.content,
                        style: const TextStyle(fontSize: 16, height: 1.4),
                      ),

                      // Tweet image (if any)
                      if (tweet.imageUrl != null) ...[
                        const SizedBox(height: 12),
                        ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: Image.network(
                            tweet.imageUrl!,
                            width: double.infinity,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                height: 200,
                                color: Colors.grey[200],
                                child: const Center(
                                  child: Icon(
                                    Icons.broken_image,
                                    color: Colors.grey,
                                    size: 48,
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ],

                      const SizedBox(height: 12),

                      // Action buttons
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // Reply button
                          _ActionButton(
                            icon: Icons.chat_bubble_outline,
                            count: tweet.repliesCount,
                            onTap: onReply,
                            color: Colors.grey[600]!,
                          ),

                          // Retweet button
                          _ActionButton(
                            icon:
                                tweet.isRetweeted ? Icons.repeat : Icons.repeat,
                            count: tweet.retweetsCount,
                            onTap: onRetweet,
                            color:
                                tweet.isRetweeted
                                    ? Colors.green
                                    : Colors.grey[600]!,
                            isActive: tweet.isRetweeted,
                          ),

                          // Like button
                          _ActionButton(
                            icon:
                                tweet.isLiked
                                    ? Icons.favorite
                                    : Icons.favorite_border,
                            count: tweet.likesCount,
                            onTap: onLike,
                            color:
                                tweet.isLiked ? Colors.red : Colors.grey[600]!,
                            isActive: tweet.isLiked,
                          ),

                          // Share button
                          _ActionButton(
                            icon: Icons.share_outlined,
                            onTap:
                                onShare ??
                                () {
                                  // Default share functionality
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text(
                                        'Share functionality coming soon!',
                                      ),
                                    ),
                                  );
                                },
                            color: Colors.grey[600]!,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class _ActionButton extends StatelessWidget {
  final IconData icon;
  final int? count;
  final VoidCallback? onTap;
  final Color color;
  final bool isActive;

  const _ActionButton({
    required this.icon,
    this.count,
    this.onTap,
    required this.color,
    this.isActive = false,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(20),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 18, color: color),
            if (count != null && count! > 0) ...[
              const SizedBox(width: 4),
              Text(
                _formatCount(count!),
                style: TextStyle(
                  color: color,
                  fontSize: 13,
                  fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  String _formatCount(int count) {
    if (count < 1000) {
      return count.toString();
    } else if (count < 1000000) {
      return '${(count / 1000).toStringAsFixed(1)}K';
    } else {
      return '${(count / 1000000).toStringAsFixed(1)}M';
    }
  }
}
