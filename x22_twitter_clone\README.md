# X22 - Twitter Clone

A full-stack Twitter clone built with Flutter and PHP, featuring real-time social media functionality.

## Features

- ✅ User Authentication (Register/Login)
- ✅ Tweet Creation and Display
- ✅ Like/Unlike Tweets
- ✅ Retweet Functionality
- ✅ Real-time Feed
- ✅ User Profiles
- ✅ Responsive Design
- ✅ MySQL Database Integration

## Tech Stack

### Frontend
- **Flutter** - Cross-platform mobile/web framework
- **Provider** - State management
- **HTTP** - API communication

### Backend
- **PHP** - Server-side logic
- **MySQL** - Database
- **XAMPP** - Local development environment

## Prerequisites

- Flutter SDK (3.29.3 or later)
- XAMPP with PHP and MySQL
- Web browser (Chrome recommended)

## Setup Instructions

### 1. Database Setup

1. Start XAMPP and ensure MySQL is running
2. Run the database setup script:
   ```bash
   php setup_database.php
   ```

### 2. Backend Setup

1. Ensure XAMPP Apache server is running
2. The backend API is located in `backend/` directory
3. API endpoints are accessible at `http://localhost/x22/x22_twitter_clone/backend/api/`

### 3. Flutter App Setup

1. Navigate to the project directory:
   ```bash
   cd x22_twitter_clone
   ```

2. Install dependencies:
   ```bash
   flutter pub get
   ```

3. Run the app:
   ```bash
   flutter run -d chrome --web-port=8080
   ```

## API Endpoints

### Authentication
- `POST /api/auth/register.php` - User registration
- `POST /api/auth/login.php` - User login

### Tweets
- `GET /api/tweets/read.php` - Get tweets
- `POST /api/tweets/create.php` - Create tweet

## Database Schema

The application uses the following main tables:
- `users` - User accounts and profiles
- `tweets` - Tweet content and metadata
- `likes` - Tweet likes
- `follows` - User relationships
- `notifications` - User notifications

## Usage

1. **Registration**: Create a new account with username, email, and password
2. **Login**: Sign in with your credentials
3. **Create Tweets**: Use the compose button to create new tweets
4. **Interact**: Like, retweet, and reply to tweets
5. **Timeline**: View tweets from all users in chronological order

## Project Structure

```
x22_twitter_clone/
├── lib/
│   ├── models/          # Data models
│   ├── providers/       # State management
│   ├── screens/         # UI screens
│   ├── services/        # API services
│   ├── widgets/         # Reusable widgets
│   └── main.dart        # App entry point
├── backend/
│   ├── api/             # API endpoints
│   ├── config/          # Database configuration
│   └── models/          # PHP models
└── database/
    └── schema.sql       # Database schema
```

## Configuration

### Database Configuration
Update database credentials in `backend/config/database.php`:
```php
private $host = "localhost";
private $db_name = "x22";
private $username = "root";
private $password = "as102030.KK";
```

### API Configuration
Update API base URL in `lib/services/api_service.dart`:
```dart
static const String baseUrl = 'http://localhost/x22/x22_twitter_clone/backend/api';
```

## Development

### Running in Development Mode
```bash
flutter run -d chrome --web-port=8080
```

### Building for Production
```bash
flutter build web
```

## Troubleshooting

1. **Database Connection Issues**: Ensure XAMPP MySQL is running and credentials are correct
2. **API Errors**: Check that Apache server is running and PHP files are accessible
3. **CORS Issues**: Ensure proper CORS headers are set in PHP files
4. **Flutter Issues**: Run `flutter clean` and `flutter pub get`

## Future Enhancements

- [ ] Image upload for tweets
- [ ] Direct messaging
- [ ] Push notifications
- [ ] Advanced search
- [ ] Trending topics
- [ ] User verification system
- [ ] Mobile app deployment

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is for educational purposes. Feel free to use and modify as needed.
