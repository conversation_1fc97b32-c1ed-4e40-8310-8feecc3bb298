// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'X22';

  @override
  String get home => 'Home';

  @override
  String get profile => 'Profile';

  @override
  String get trends => 'Trends';

  @override
  String get settings => 'Settings';

  @override
  String get notifications => 'Notifications';

  @override
  String get bookmarks => 'Bookmarks';

  @override
  String get help => 'Help';

  @override
  String get logout => 'Logout';

  @override
  String get login => 'Login';

  @override
  String get register => 'Register';

  @override
  String get email => 'Email';

  @override
  String get password => 'Password';

  @override
  String get username => 'Username';

  @override
  String get displayName => 'Display Name';

  @override
  String get bio => 'Bio';

  @override
  String get confirmPassword => 'Confirm Password';

  @override
  String get createAccount => 'Create Account';

  @override
  String get signIn => 'Sign In';

  @override
  String get welcomeBack => 'Welcome back';

  @override
  String get signInToAccount => 'Sign in to your account';

  @override
  String get joinToday => 'Join X22 today';

  @override
  String get alreadyHaveAccount => 'Already have an account? Sign in';

  @override
  String get dontHaveAccount => 'Don\'t have an account? Sign up';

  @override
  String get whatsHappening => 'What\'s happening?';

  @override
  String get postYourReply => 'Post your reply';

  @override
  String get post => 'Post';

  @override
  String get reply => 'Reply';

  @override
  String get retweet => 'Retweet';

  @override
  String get like => 'Like';

  @override
  String get share => 'Share';

  @override
  String get following => 'Following';

  @override
  String get followers => 'Followers';

  @override
  String get tweets => 'Tweets';

  @override
  String get replies => 'Replies';

  @override
  String get media => 'Media';

  @override
  String get noTweetsYet => 'No tweets yet';

  @override
  String get beFirstToShare => 'Be the first to share something!';

  @override
  String get failedToLoadTweets => 'Failed to load tweets';

  @override
  String get retry => 'Retry';

  @override
  String get trendingIn => 'Trending in';

  @override
  String get searchFor => 'Search for';

  @override
  String get notInterestedInTrend => 'Not interested in this trend';

  @override
  String get report => 'Report';

  @override
  String get account => 'Account';

  @override
  String get privacyAndSecurity => 'Privacy and Security';

  @override
  String get blockedAccounts => 'Blocked Accounts';

  @override
  String get preferences => 'Preferences';

  @override
  String get darkMode => 'Dark Mode';

  @override
  String get useDarkTheme => 'Use dark theme';

  @override
  String get autoPlayVideos => 'Auto-play Videos';

  @override
  String get videosPlayAutomatically => 'Videos play automatically';

  @override
  String get language => 'Language';

  @override
  String get content => 'Content';

  @override
  String get dataUsage => 'Data Usage';

  @override
  String get manageDownloadPreferences => 'Manage download preferences';

  @override
  String get contentFilters => 'Content Filters';

  @override
  String get manageWhatContentYouSee => 'Manage what content you see';

  @override
  String get support => 'Support';

  @override
  String get helpCenter => 'Help Center';

  @override
  String get sendFeedback => 'Send Feedback';

  @override
  String get aboutX22 => 'About X22';

  @override
  String get receiveNotifications => 'Receive push notifications';

  @override
  String get changePassword => 'Change Password';

  @override
  String get whoCanSeeYourTweets => 'Who can see your tweets';

  @override
  String get selectLanguage => 'Select Language';

  @override
  String get tellUsWhatYouThink => 'Tell us what you think...';

  @override
  String get cancel => 'Cancel';

  @override
  String get send => 'Send';

  @override
  String get thankYouForFeedback => 'Thank you for your feedback!';

  @override
  String get areYouSureLogout => 'Are you sure you want to logout?';

  @override
  String get connectWithWorld => 'Connect with the world';

  @override
  String get pleaseEnterEmail => 'Please enter your email';

  @override
  String get pleaseEnterValidEmail => 'Please enter a valid email';

  @override
  String get pleaseEnterPassword => 'Please enter your password';

  @override
  String get passwordMustBe6Chars => 'Password must be at least 6 characters';

  @override
  String get pleaseEnterUsername => 'Please enter a username';

  @override
  String get usernameMustBe3Chars => 'Username must be at least 3 characters';

  @override
  String get usernameOnlyLettersNumbers => 'Username can only contain letters, numbers, and underscores';

  @override
  String get pleaseEnterDisplayName => 'Please enter your display name';

  @override
  String get displayNameMustBe2Chars => 'Display name must be at least 2 characters';

  @override
  String get pleaseConfirmPassword => 'Please confirm your password';

  @override
  String get passwordsDoNotMatch => 'Passwords do not match';

  @override
  String get bioOptional => 'Bio (optional)';

  @override
  String get aboutYourself => 'Tell us about yourself...';

  @override
  String get accountCreatedSuccessfully => 'Account created successfully! Please sign in.';

  @override
  String get invalidPassword => 'Invalid password.';

  @override
  String get userNotFound => 'User not found.';

  @override
  String get emailAndPasswordRequired => 'Email and password are required.';

  @override
  String get networkError => 'Network error';

  @override
  String get loginFailed => 'Login failed';

  @override
  String get registrationFailed => 'Registration failed';

  @override
  String get failedToCreateTweet => 'Failed to create tweet';

  @override
  String get tweetCreatedSuccessfully => 'Tweet was created successfully.';

  @override
  String get unableToCreateTweet => 'Unable to create tweet. Content is required.';

  @override
  String get composeTweet => 'Compose Tweet';

  @override
  String get imageUploadComingSoon => 'Image upload coming soon!';

  @override
  String get gifSupportComingSoon => 'GIF support coming soon!';

  @override
  String get shareComingSoon => 'Share functionality coming soon!';

  @override
  String get notificationsComingSoon => 'Notifications coming soon!';

  @override
  String get bookmarksComingSoon => 'Bookmarks coming soon!';

  @override
  String get helpComingSoon => 'Help coming soon!';

  @override
  String get editProfileComingSoon => 'Edit profile coming soon!';

  @override
  String get blockedAccountsComingSoon => 'Blocked accounts coming soon!';

  @override
  String get darkModeComingSoon => 'Dark mode coming soon!';

  @override
  String get dataUsageComingSoon => 'Data usage settings coming soon!';

  @override
  String get contentFiltersComingSoon => 'Content filters coming soon!';

  @override
  String get helpCenterComingSoon => 'Help center coming soon!';

  @override
  String get changePasswordComingSoon => 'Change password coming soon!';

  @override
  String get privacySettingsComingSoon => 'Privacy settings coming soon!';

  @override
  String get repliesComingSoon => 'Replies coming soon!';

  @override
  String get mediaComingSoon => 'Media coming soon!';

  @override
  String get searchingFor => 'Searching for';

  @override
  String get wontSeeThisTrend => 'You won\'t see this trend anymore';

  @override
  String get trendReported => 'Trend reported';

  @override
  String get failedToLoadUserTweets => 'Failed to load tweets';

  @override
  String get version => 'v1.0.0';
}
