<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../../config/database.php';
include_once '../../models/Tweet.php';

// Get authorization header
$headers = apache_request_headers();
$token = isset($headers['Authorization']) ? str_replace('Bearer ', '', $headers['Authorization']) : '';

if(empty($token)) {
    http_response_code(401);
    echo json_encode(array("message" => "Access denied. Token required."));
    exit();
}

// Decode token (simple base64 decode - in production use JWT)
$decoded_token = json_decode(base64_decode($token), true);

if(!$decoded_token || !isset($decoded_token['user_id']) || $decoded_token['exp'] < time()) {
    http_response_code(401);
    echo json_encode(array("message" => "Access denied. Invalid or expired token."));
    exit();
}

$database = new Database();
$db = $database->getConnection();

$tweet = new Tweet($db);

// Get posted data
$data = json_decode(file_get_contents("php://input"));

if(!empty($data->tweet_id)) {
    $tweet->id = $data->tweet_id;
    $user_id = $decoded_token['user_id'];
    
    if($tweet->unlike($user_id)) {
        http_response_code(200);
        echo json_encode(array("message" => "Tweet unliked successfully."));
    } else {
        http_response_code(400);
        echo json_encode(array("message" => "Tweet not liked or unable to unlike."));
    }
} else {
    http_response_code(400);
    echo json_encode(array("message" => "Unable to unlike tweet. Tweet ID is required."));
}
?>
