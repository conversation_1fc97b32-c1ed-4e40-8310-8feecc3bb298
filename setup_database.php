<?php
// Simple script to set up the database
$host = "localhost";
$username = "root";
$password = "as102030.KK";

try {
    // Connect to MySQL server
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Connected to MySQL server successfully.\n";
    
    // Read and execute the schema file
    $schemaFile = 'x22_twitter_clone/database/schema.sql';
    if (file_exists($schemaFile)) {
        $sql = file_get_contents($schemaFile);
        
        // Split the SQL into individual statements
        $statements = array_filter(array_map('trim', explode(';', $sql)));
        
        foreach ($statements as $statement) {
            if (!empty($statement)) {
                $pdo->exec($statement);
            }
        }
        
        echo "Database schema created successfully!\n";
        echo "Database 'x22' is ready to use.\n";
        echo "You can now run the Flutter app.\n";
    } else {
        echo "Schema file not found: $schemaFile\n";
    }
    
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
