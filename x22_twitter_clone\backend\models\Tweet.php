<?php
class Tweet {
    private $conn;
    private $table_name = "tweets";

    public $id;
    public $user_id;
    public $content;
    public $image_url;
    public $reply_to_tweet_id;
    public $retweet_of_tweet_id;
    public $likes_count;
    public $retweets_count;
    public $replies_count;
    public $created_at;
    public $updated_at;

    public function __construct($db) {
        $this->conn = $db;
    }

    // Create tweet
    function create() {
        $query = "INSERT INTO " . $this->table_name . " 
                SET user_id=:user_id, content=:content, image_url=:image_url, 
                    reply_to_tweet_id=:reply_to_tweet_id, retweet_of_tweet_id=:retweet_of_tweet_id";

        $stmt = $this->conn->prepare($query);

        // Sanitize
        $this->content = htmlspecialchars(strip_tags($this->content));
        $this->image_url = htmlspecialchars(strip_tags($this->image_url));

        // Bind values
        $stmt->bindParam(":user_id", $this->user_id);
        $stmt->bindParam(":content", $this->content);
        $stmt->bindParam(":image_url", $this->image_url);
        $stmt->bindParam(":reply_to_tweet_id", $this->reply_to_tweet_id);
        $stmt->bindParam(":retweet_of_tweet_id", $this->retweet_of_tweet_id);

        if($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            
            // Update user's tweet count
            $update_query = "UPDATE users SET tweets_count = tweets_count + 1 WHERE id = :user_id";
            $update_stmt = $this->conn->prepare($update_query);
            $update_stmt->bindParam(":user_id", $this->user_id);
            $update_stmt->execute();
            
            return true;
        }

        return false;
    }

    // Read tweets (timeline)
    function read($limit = 20, $offset = 0) {
        $query = "SELECT 
                    t.id, t.user_id, t.content, t.image_url, t.reply_to_tweet_id, 
                    t.retweet_of_tweet_id, t.likes_count, t.retweets_count, 
                    t.replies_count, t.created_at,
                    u.username, u.display_name, u.profile_image_url, u.verified
                  FROM " . $this->table_name . " t
                  LEFT JOIN users u ON t.user_id = u.id
                  ORDER BY t.created_at DESC
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":limit", $limit, PDO::PARAM_INT);
        $stmt->bindParam(":offset", $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt;
    }

    // Read tweets by user
    function readByUser($user_id, $limit = 20, $offset = 0) {
        $query = "SELECT 
                    t.id, t.user_id, t.content, t.image_url, t.reply_to_tweet_id, 
                    t.retweet_of_tweet_id, t.likes_count, t.retweets_count, 
                    t.replies_count, t.created_at,
                    u.username, u.display_name, u.profile_image_url, u.verified
                  FROM " . $this->table_name . " t
                  LEFT JOIN users u ON t.user_id = u.id
                  WHERE t.user_id = :user_id
                  ORDER BY t.created_at DESC
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":user_id", $user_id);
        $stmt->bindParam(":limit", $limit, PDO::PARAM_INT);
        $stmt->bindParam(":offset", $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt;
    }

    // Read single tweet
    function readOne() {
        $query = "SELECT 
                    t.id, t.user_id, t.content, t.image_url, t.reply_to_tweet_id, 
                    t.retweet_of_tweet_id, t.likes_count, t.retweets_count, 
                    t.replies_count, t.created_at,
                    u.username, u.display_name, u.profile_image_url, u.verified
                  FROM " . $this->table_name . " t
                  LEFT JOIN users u ON t.user_id = u.id
                  WHERE t.id = ? LIMIT 0,1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $this->id);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        if($row) {
            $this->user_id = $row['user_id'];
            $this->content = $row['content'];
            $this->image_url = $row['image_url'];
            $this->reply_to_tweet_id = $row['reply_to_tweet_id'];
            $this->retweet_of_tweet_id = $row['retweet_of_tweet_id'];
            $this->likes_count = $row['likes_count'];
            $this->retweets_count = $row['retweets_count'];
            $this->replies_count = $row['replies_count'];
            $this->created_at = $row['created_at'];
            return true;
        }

        return false;
    }

    // Delete tweet
    function delete() {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = ? AND user_id = ?";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $this->id);
        $stmt->bindParam(2, $this->user_id);

        if($stmt->execute()) {
            // Update user's tweet count
            $update_query = "UPDATE users SET tweets_count = tweets_count - 1 WHERE id = :user_id";
            $update_stmt = $this->conn->prepare($update_query);
            $update_stmt->bindParam(":user_id", $this->user_id);
            $update_stmt->execute();
            
            return true;
        }

        return false;
    }

    // Like tweet
    function like($user_id) {
        // Check if already liked
        $check_query = "SELECT id FROM likes WHERE user_id = ? AND tweet_id = ?";
        $check_stmt = $this->conn->prepare($check_query);
        $check_stmt->bindParam(1, $user_id);
        $check_stmt->bindParam(2, $this->id);
        $check_stmt->execute();

        if($check_stmt->rowCount() > 0) {
            return false; // Already liked
        }

        // Add like
        $like_query = "INSERT INTO likes (user_id, tweet_id) VALUES (?, ?)";
        $like_stmt = $this->conn->prepare($like_query);
        $like_stmt->bindParam(1, $user_id);
        $like_stmt->bindParam(2, $this->id);

        if($like_stmt->execute()) {
            // Update likes count
            $update_query = "UPDATE " . $this->table_name . " SET likes_count = likes_count + 1 WHERE id = ?";
            $update_stmt = $this->conn->prepare($update_query);
            $update_stmt->bindParam(1, $this->id);
            $update_stmt->execute();
            return true;
        }

        return false;
    }

    // Unlike tweet
    function unlike($user_id) {
        $unlike_query = "DELETE FROM likes WHERE user_id = ? AND tweet_id = ?";
        $unlike_stmt = $this->conn->prepare($unlike_query);
        $unlike_stmt->bindParam(1, $user_id);
        $unlike_stmt->bindParam(2, $this->id);

        if($unlike_stmt->execute() && $unlike_stmt->rowCount() > 0) {
            // Update likes count
            $update_query = "UPDATE " . $this->table_name . " SET likes_count = likes_count - 1 WHERE id = ?";
            $update_stmt = $this->conn->prepare($update_query);
            $update_stmt->bindParam(1, $this->id);
            $update_stmt->execute();
            return true;
        }

        return false;
    }
}
?>
