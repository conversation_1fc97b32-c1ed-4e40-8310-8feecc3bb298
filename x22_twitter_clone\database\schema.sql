-- X22 Twitter Clone Database Schema
-- Database: x22
-- Username: root
-- Password: as102030.KK

CREATE DATABASE IF NOT EXISTS x22;
USE x22;

-- Users table
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VA<PERSON>HA<PERSON>(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    display_name VA<PERSON>HA<PERSON>(100) NOT NULL,
    bio TEXT,
    profile_image_url VARCHAR(255),
    cover_image_url VARCHAR(255),
    followers_count INT DEFAULT 0,
    following_count INT DEFAULT 0,
    tweets_count INT DEFAULT 0,
    verified <PERSON><PERSON><PERSON><PERSON><PERSON> DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tweets table
CREATE TABLE tweets (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    content TEXT NOT NULL,
    image_url VARCHAR(255),
    reply_to_tweet_id INT NULL,
    retweet_of_tweet_id INT NULL,
    likes_count INT DEFAULT 0,
    retweets_count INT DEFAULT 0,
    replies_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (reply_to_tweet_id) REFERENCES tweets(id) ON DELETE CASCADE,
    FOREIGN KEY (retweet_of_tweet_id) REFERENCES tweets(id) ON DELETE CASCADE
);

-- Follows table (user relationships)
CREATE TABLE follows (
    id INT PRIMARY KEY AUTO_INCREMENT,
    follower_id INT NOT NULL,
    following_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (follower_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (following_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_follow (follower_id, following_id)
);

-- Likes table
CREATE TABLE likes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    tweet_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (tweet_id) REFERENCES tweets(id) ON DELETE CASCADE,
    UNIQUE KEY unique_like (user_id, tweet_id)
);

-- Retweets table
CREATE TABLE retweets (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    tweet_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (tweet_id) REFERENCES tweets(id) ON DELETE CASCADE,
    UNIQUE KEY unique_retweet (user_id, tweet_id)
);

-- Notifications table
CREATE TABLE notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    type ENUM('like', 'retweet', 'follow', 'reply', 'mention') NOT NULL,
    from_user_id INT NOT NULL,
    tweet_id INT NULL,
    message TEXT,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (from_user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (tweet_id) REFERENCES tweets(id) ON DELETE CASCADE
);

-- Hashtags table
CREATE TABLE hashtags (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tag VARCHAR(100) UNIQUE NOT NULL,
    usage_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tweet hashtags junction table
CREATE TABLE tweet_hashtags (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tweet_id INT NOT NULL,
    hashtag_id INT NOT NULL,
    FOREIGN KEY (tweet_id) REFERENCES tweets(id) ON DELETE CASCADE,
    FOREIGN KEY (hashtag_id) REFERENCES hashtags(id) ON DELETE CASCADE,
    UNIQUE KEY unique_tweet_hashtag (tweet_id, hashtag_id)
);

-- Mentions table
CREATE TABLE mentions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tweet_id INT NOT NULL,
    user_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tweet_id) REFERENCES tweets(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_mention (tweet_id, user_id)
);

-- Create indexes for better performance
CREATE INDEX idx_tweets_user_id ON tweets(user_id);
CREATE INDEX idx_tweets_created_at ON tweets(created_at);
CREATE INDEX idx_follows_follower_id ON follows(follower_id);
CREATE INDEX idx_follows_following_id ON follows(following_id);
CREATE INDEX idx_likes_user_id ON likes(user_id);
CREATE INDEX idx_likes_tweet_id ON likes(tweet_id);
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_created_at ON notifications(created_at);

-- Insert sample data
INSERT INTO users (username, email, password_hash, display_name, bio) VALUES
('john_doe', '<EMAIL>', '$2y$10$example_hash_1', 'John Doe', 'Software developer and tech enthusiast'),
('jane_smith', '<EMAIL>', '$2y$10$example_hash_2', 'Jane Smith', 'Designer and creative thinker'),
('tech_guru', '<EMAIL>', '$2y$10$example_hash_3', 'Tech Guru', 'Sharing the latest in technology');

INSERT INTO tweets (user_id, content) VALUES
(1, 'Hello world! This is my first tweet on X22!'),
(2, 'Excited to be part of this new social platform!'),
(3, 'The future of social media is here. #X22 #SocialMedia'),
(1, 'Working on some exciting new features. Stay tuned!');

INSERT INTO hashtags (tag, usage_count) VALUES
('X22', 1),
('SocialMedia', 1),
('Technology', 0),
('Flutter', 0),
('MySQL', 0);
