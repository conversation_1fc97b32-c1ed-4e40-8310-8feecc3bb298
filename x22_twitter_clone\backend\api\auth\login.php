<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(200);
    exit();
}

include_once '../../config/database.php';
include_once '../../models/User.php';

$database = new Database();
$db = $database->getConnection();

$user = new User($db);

$data = json_decode(file_get_contents("php://input"));

if(!empty($data->email) && !empty($data->password)) {
    $user->email = $data->email;
    
    if($user->readByEmail()) {
        if(password_verify($data->password, $user->password_hash)) {
            // Create a simple token (in production, use JWT)
            $token = base64_encode(json_encode(array(
                "user_id" => $user->id,
                "username" => $user->username,
                "email" => $user->email,
                "exp" => time() + (24 * 60 * 60) // 24 hours
            )));

            http_response_code(200);
            echo json_encode(array(
                "message" => "Login successful.",
                "token" => $token,
                "user" => array(
                    "id" => $user->id,
                    "username" => $user->username,
                    "email" => $user->email,
                    "display_name" => $user->display_name,
                    "bio" => $user->bio,
                    "profile_image_url" => $user->profile_image_url,
                    "cover_image_url" => $user->cover_image_url,
                    "followers_count" => $user->followers_count,
                    "following_count" => $user->following_count,
                    "tweets_count" => $user->tweets_count,
                    "verified" => $user->verified,
                    "created_at" => $user->created_at
                )
            ));
        } else {
            http_response_code(401);
            echo json_encode(array("message" => "Invalid password."));
        }
    } else {
        http_response_code(401);
        echo json_encode(array("message" => "User not found."));
    }
} else {
    http_response_code(400);
    echo json_encode(array("message" => "Email and password are required."));
}
?>
