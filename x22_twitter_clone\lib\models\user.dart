class User {
  final int? id;
  final String username;
  final String? email;
  final String displayName;
  final String? bio;
  final String? profileImageUrl;
  final String? coverImageUrl;
  final int followersCount;
  final int followingCount;
  final int tweetsCount;
  final bool verified;
  final DateTime? createdAt;

  User({
    this.id,
    required this.username,
    this.email,
    required this.displayName,
    this.bio,
    this.profileImageUrl,
    this.coverImageUrl,
    this.followersCount = 0,
    this.followingCount = 0,
    this.tweetsCount = 0,
    this.verified = false,
    this.createdAt,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      username: json['username'] ?? '',
      email: json['email'],
      displayName: json['display_name'] ?? '',
      bio: json['bio'],
      profileImageUrl: json['profile_image_url'],
      coverImageUrl: json['cover_image_url'],
      followersCount: json['followers_count'] ?? 0,
      followingCount: json['following_count'] ?? 0,
      tweetsCount: json['tweets_count'] ?? 0,
      verified: json['verified'] == 1 || json['verified'] == true,
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'])
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'email': email,
      'display_name': displayName,
      'bio': bio,
      'profile_image_url': profileImageUrl,
      'cover_image_url': coverImageUrl,
      'followers_count': followersCount,
      'following_count': followingCount,
      'tweets_count': tweetsCount,
      'verified': verified,
      'created_at': createdAt?.toIso8601String(),
    };
  }

  User copyWith({
    int? id,
    String? username,
    String? email,
    String? displayName,
    String? bio,
    String? profileImageUrl,
    String? coverImageUrl,
    int? followersCount,
    int? followingCount,
    int? tweetsCount,
    bool? verified,
    DateTime? createdAt,
  }) {
    return User(
      id: id ?? this.id,
      username: username ?? this.username,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      bio: bio ?? this.bio,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      coverImageUrl: coverImageUrl ?? this.coverImageUrl,
      followersCount: followersCount ?? this.followersCount,
      followingCount: followingCount ?? this.followingCount,
      tweetsCount: tweetsCount ?? this.tweetsCount,
      verified: verified ?? this.verified,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}
