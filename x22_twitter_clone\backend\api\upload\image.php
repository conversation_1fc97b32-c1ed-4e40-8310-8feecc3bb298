<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Get authorization header
$headers = apache_request_headers();
$token = isset($headers['Authorization']) ? str_replace('Bearer ', '', $headers['Authorization']) : '';

if(empty($token)) {
    http_response_code(401);
    echo json_encode(array("message" => "Access denied. Token required."));
    exit();
}

// Decode token (simple base64 decode - in production use JWT)
$decoded_token = json_decode(base64_decode($token), true);

if(!$decoded_token || !isset($decoded_token['user_id']) || $decoded_token['exp'] < time()) {
    http_response_code(401);
    echo json_encode(array("message" => "Access denied. Invalid or expired token."));
    exit();
}

// Check if image file is uploaded
if (!isset($_FILES['image']) || $_FILES['image']['error'] !== UPLOAD_ERR_OK) {
    http_response_code(400);
    echo json_encode(array("message" => "No image file uploaded or upload error."));
    exit();
}

$uploadedFile = $_FILES['image'];

// Validate file type
$allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
$fileType = $uploadedFile['type'];

if (!in_array($fileType, $allowedTypes)) {
    http_response_code(400);
    echo json_encode(array("message" => "Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed."));
    exit();
}

// Validate file size (max 5MB)
$maxSize = 5 * 1024 * 1024; // 5MB in bytes
if ($uploadedFile['size'] > $maxSize) {
    http_response_code(400);
    echo json_encode(array("message" => "File too large. Maximum size is 5MB."));
    exit();
}

// Create uploads directory if it doesn't exist
$uploadsDir = '../../uploads/images/';
if (!file_exists($uploadsDir)) {
    mkdir($uploadsDir, 0755, true);
}

// Generate unique filename
$fileExtension = pathinfo($uploadedFile['name'], PATHINFO_EXTENSION);
$fileName = uniqid() . '_' . time() . '.' . $fileExtension;
$filePath = $uploadsDir . $fileName;

// Move uploaded file
if (move_uploaded_file($uploadedFile['tmp_name'], $filePath)) {
    // Return the URL to access the image
    $imageUrl = 'http://localhost/x22/x22_twitter_clone/backend/uploads/images/' . $fileName;
    
    http_response_code(200);
    echo json_encode(array(
        "message" => "Image uploaded successfully.",
        "image_url" => $imageUrl
    ));
} else {
    http_response_code(500);
    echo json_encode(array("message" => "Failed to save uploaded file."));
}
?>
