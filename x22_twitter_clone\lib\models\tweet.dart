import 'user.dart';

class Tweet {
  final int id;
  final int userId;
  final String content;
  final String? imageUrl;
  final int? replyToTweetId;
  final int? retweetOfTweetId;
  final int likesCount;
  final int retweetsCount;
  final int repliesCount;
  final DateTime createdAt;
  final User user;
  final bool isLiked;
  final bool isRetweeted;

  Tweet({
    required this.id,
    required this.userId,
    required this.content,
    this.imageUrl,
    this.replyToTweetId,
    this.retweetOfTweetId,
    required this.likesCount,
    required this.retweetsCount,
    required this.repliesCount,
    required this.createdAt,
    required this.user,
    this.isLiked = false,
    this.isRetweeted = false,
  });

  factory Tweet.fromJson(Map<String, dynamic> json) {
    return Tweet(
      id: json['id'],
      userId: json['user_id'],
      content: json['content'],
      imageUrl: json['image_url'],
      replyToTweetId: json['reply_to_tweet_id'],
      retweetOfTweetId: json['retweet_of_tweet_id'],
      likesCount: json['likes_count'] ?? 0,
      retweetsCount: json['retweets_count'] ?? 0,
      repliesCount: json['replies_count'] ?? 0,
      createdAt: DateTime.parse(json['created_at']),
      user: User.fromJson(json['user']),
      isLiked: json['is_liked'] ?? false,
      isRetweeted: json['is_retweeted'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'content': content,
      'image_url': imageUrl,
      'reply_to_tweet_id': replyToTweetId,
      'retweet_of_tweet_id': retweetOfTweetId,
      'likes_count': likesCount,
      'retweets_count': retweetsCount,
      'replies_count': repliesCount,
      'created_at': createdAt.toIso8601String(),
      'user': user.toJson(),
      'is_liked': isLiked,
      'is_retweeted': isRetweeted,
    };
  }

  Tweet copyWith({
    int? id,
    int? userId,
    String? content,
    String? imageUrl,
    int? replyToTweetId,
    int? retweetOfTweetId,
    int? likesCount,
    int? retweetsCount,
    int? repliesCount,
    DateTime? createdAt,
    User? user,
    bool? isLiked,
    bool? isRetweeted,
  }) {
    return Tweet(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      content: content ?? this.content,
      imageUrl: imageUrl ?? this.imageUrl,
      replyToTweetId: replyToTweetId ?? this.replyToTweetId,
      retweetOfTweetId: retweetOfTweetId ?? this.retweetOfTweetId,
      likesCount: likesCount ?? this.likesCount,
      retweetsCount: retweetsCount ?? this.retweetsCount,
      repliesCount: repliesCount ?? this.repliesCount,
      createdAt: createdAt ?? this.createdAt,
      user: user ?? this.user,
      isLiked: isLiked ?? this.isLiked,
      isRetweeted: isRetweeted ?? this.isRetweeted,
    );
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return '${difference.inDays}d';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m';
    } else {
      return 'now';
    }
  }

  bool get isReply => replyToTweetId != null;
  bool get isRetweet => retweetOfTweetId != null;
}
